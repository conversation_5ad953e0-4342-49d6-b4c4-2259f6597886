-- Migration: Add InputPrice and ProfitMargin columns to Product table
-- Date: 2025-01-27
-- Description: Add InputPrice and ProfitMargin columns to support profit calculation

BEGIN;

-- Step 1: Add InputPrice column to Product table
ALTER TABLE "Product" 
ADD COLUMN IF NOT EXISTS "InputPrice" DECIMAL(18,2) NULL;

-- Step 2: Add ProfitMargin column to Product table  
ALTER TABLE "Product" 
ADD COLUMN IF NOT EXISTS "ProfitMargin" DECIMAL(5,2) NULL;

-- Step 3: Add comments to clarify the purpose
COMMENT ON COLUMN "Product"."InputPrice" IS 'Input/purchase price for the product (VND)';
COMMENT ON COLUMN "Product"."ProfitMargin" IS 'Profit margin percentage (%)';
COMMENT ON COLUMN "Product"."DefaultPrice" IS 'Default selling price calculated from InputPrice and ProfitMargin (VND)';

-- Step 4: Create indexes for better query performance (optional)
CREATE INDEX IF NOT EXISTS "IX_Product_InputPrice" ON "Product" ("InputPrice");
CREATE INDEX IF NOT EXISTS "IX_Product_ProfitMargin" ON "Product" ("ProfitMargin");

-- Step 5: Update existing records to set InputPrice based on DefaultPrice if needed
-- This is optional and can be customized based on business requirements
-- UPDATE "Product" 
-- SET "InputPrice" = "DefaultPrice" * 0.8 
-- WHERE "DefaultPrice" IS NOT NULL AND "InputPrice" IS NULL;

COMMIT;
