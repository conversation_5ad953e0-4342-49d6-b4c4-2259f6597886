﻿using Microsoft.OpenApi.Attributes;
using System.ComponentModel;

namespace Tasin.Website.Common.Enums
{
    /// Rule for naming : E + Noun

    /// <summary>
    /// Encode password type
    /// </summary>
    public enum EEncodeType
    {
        SHA_256
    }

    /// <summary>
    /// Enum file type
    /// </summary>
    public enum EFileType
    {
        Excel,
        //Word,...
    }

    public enum ECategoryType
    {
        [Display("Loại khách hàng")]
        CustomerType,
        CommonStatusType,
        Unit,
        ProcessingType,
        Material,
        Vendor,
        Customer,
        Product,
        User,
        POStatus,
        PAStatus,
        PaymentMethod
    }

    public enum ECommonStatus
    {
        [Description("Đang hoạt động")]
        Actived,
        [Description("Không hoạt động")]
        InActived,
    }
    public enum ECustomerType
    {
        [Description("Doanh nghiệp")]
        Company,
        [Description("Cá nhân")]
        Individual,
    }

    /// <summary>
    /// Processing type enum for products
    /// </summary>
    public enum EProcessingType
    {
        [Description("Nguyên liệu")]
        Material = 1,
        [Description("Sơ chế")]
        SemiProcessed = 2,
        [Description("Thành phẩm")]
        FinishedProduct = 3
    }
    public enum EPOStatus
    {
        [Description("Mới")]
        New,
        [Description("Đã xác nhận")]
        Confirmed,
        [Description("Đã tạo đơn tổng hợp")]
        Executed,
        [Description("Đã hủy")]
        Cancel,
    }

    public enum EPAStatus
    {
        [Description("Mới")]
        New,
        [Description("Đã gửi NCC")]
        SendVendor,
        [Description("Gửi email thất bại")]
        EmailFailed,
        [Description("Gửi email một phần")]
        PartialEmailSent,
        [Description("Hủy")]
        Cancel,
        [Description("Hoàn thành")]
        Completed,
    }

    /// <summary>
    /// Payment Method Enum
    /// </summary>
    public enum EPaymentMethod
    {
        [Description("Tiền mặt")]
        Cash = 0,

        [Description("Chuyển khoản")]
        BankTransfer = 1,

        [Description("Thẻ tín dụng")]
        CreditCard = 2,

        [Description("Séc")]
        Check = 3,

        [Description("Ví điện tử")]
        EWallet = 4,

        [Description("Trả góp")]
        Installment = 5
    }

}
